using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.SceneManagement;
using UnityEngine.UI;

public class MainMenu : MonoBehaviour
{
    #region UI Panel References
    [Header("UI Panels")]
    public GameObject modePanel;
    public GameObject levelPanel;
    public GameObject loadingPanel;
    public GameObject menuPanel;
    public GameObject exitPanel;
    public GameObject settingsPanel;
    public GameObject selectButton;
    public GameObject levelMode2Panel;
    public GameObject level2BackPanel;
    #endregion

    #region Level Management
    [Header("Level Management")]
    public GameObject[] levelButtons;
    public GameObject[] level1Buttons;
    public GameObject[] level2Buttons;
    public static int selectedLevelNumber;
    #endregion

    #region Audio Settings
    [Header("Audio Settings")]
    public AudioSource buttonSound;
    public AudioSource backgroundSound;
    public Slider musicSlider;
    public Slider soundSlider;
    #endregion

    #region UI Elements
    [Header("UI Elements")]
    public Text coinsText;
    #endregion
    #region Unity Lifecycle
    void Start()
    {
        InitializeGame();
        InitializeAudioSettings();
        InitializeLevelButtons();
    }
    #endregion

    #region Initialization Methods
    private void InitializeGame()
    {
        Time.timeScale = 1;
        coinsText.text = PlayerPrefs.GetInt("coins").ToString();
    }

    private void InitializeAudioSettings()
    {
        // Initialize sliders with saved values
        musicSlider.value = PlayerPrefs.GetFloat("MusicVolume", 0.5f);
        soundSlider.value = PlayerPrefs.GetFloat("SoundVolume", 0.5f);

        // Set initial volumes
        buttonSound.volume = musicSlider.value;
        backgroundSound.volume = soundSlider.value;

        // Add listeners for slider value changes
        musicSlider.onValueChanged.AddListener(OnMusicVolumeChanged);
        soundSlider.onValueChanged.AddListener(OnSoundVolumeChanged);
    }

    private void InitializeLevelButtons()
    {
        // Initialize main level buttons
        for (int i = 0; i < levelButtons.Length; i++)
        {
            if (PlayerPrefs.GetInt("Level" + i) == 1)
            {
                levelButtons[i + 1].transform.GetChild(0).gameObject.SetActive(false);
                levelButtons[i + 1].transform.GetComponent<Button>().interactable = true;
            }
        }

        // Initialize level 1 buttons
        for (int i = 0; i < level1Buttons.Length; i++)
        {
            if (PlayerPrefs.GetInt("Lvl" + i) == 1)
            {
                level1Buttons[i + 1].transform.GetChild(0).gameObject.SetActive(false);
            }
        }

        // Initialize level 2 buttons
        for (int i = 0; i < level2Buttons.Length; i++)
        {
            if (PlayerPrefs.GetInt("Lvll" + i) == 1)
            {
                level2Buttons[i + 1].transform.GetChild(0).gameObject.SetActive(false);
            }
        }
    }
    #endregion
    #region Main Menu Navigation
    public void PlayGame()
    {
        menuPanel.SetActive(false);
        loadingPanel.SetActive(true);
        StartCoroutine(LoadModeSelection());
    }

    public void ExitGame()
    {
        menuPanel.SetActive(false);
        exitPanel.SetActive(true);
    }

    public void CancelExit()
    {
        menuPanel.SetActive(true);
        exitPanel.SetActive(false);
    }

    public void ConfirmExit()
    {
        Application.Quit();
    }

    public void BackToMenuFromMode()
    {
        menuPanel.SetActive(true);
        modePanel.SetActive(false);
    }

    public void BackToModeFromLevel()
    {
        levelPanel.SetActive(false);
        modePanel.SetActive(true);
    }

    public void BackToModeFromLevel2()
    {
        level2BackPanel.SetActive(false);
        modePanel.SetActive(true);
    }

    public void OpenSettings()
    {
        menuPanel.SetActive(false);
        settingsPanel.SetActive(true);
    }
    #endregion
    #region Settings Management
    public void SaveSettings()
    {
        menuPanel.SetActive(true);
        settingsPanel.SetActive(false);

        // Save slider values to PlayerPrefs
        PlayerPrefs.SetFloat("MusicVolume", musicSlider.value);
        PlayerPrefs.SetFloat("SoundVolume", soundSlider.value);
        PlayerPrefs.Save();
    }

    public void OnMusicVolumeChanged(float value)
    {
        buttonSound.volume = value;
        PlayerPrefs.SetFloat("MusicVolume", value);
    }

    public void OnSoundVolumeChanged(float value)
    {
        backgroundSound.volume = value;
        PlayerPrefs.SetFloat("SoundVolume", value);
    }

    public void IncreaseMusicVolume()
    {
        musicSlider.value = Mathf.Clamp01(musicSlider.value + 0.1f);
    }

    public void DecreaseMusicVolume()
    {
        musicSlider.value = Mathf.Clamp01(musicSlider.value - 0.1f);
    }

    public void IncreaseSoundVolume()
    {
        soundSlider.value = Mathf.Clamp01(soundSlider.value + 0.1f);
    }

    public void DecreaseSoundVolume()
    {
        soundSlider.value = Mathf.Clamp01(soundSlider.value - 0.1f);
    }
    #endregion
    #region Level Selection
    public void SelectLevel(int levelNumber)
    {
        selectedLevelNumber = levelNumber;
        loadingPanel.SetActive(true);
        StartCoroutine(LoadGameplay());
    }

    public void SelectFarmingLevel(int levelNumber)
    {
        selectedLevelNumber = levelNumber;
        loadingPanel.SetActive(true);
        StartCoroutine(LoadFarmingMode());
    }

    public void SelectLevel2(int levelNumber)
    {
        selectedLevelNumber = levelNumber;
        level2BackPanel.SetActive(false);
        loadingPanel.SetActive(true);
        StartCoroutine(LoadGameplay());
    }

    public void SelectCurrentLevel()
    {
        StartCoroutine(LoadGameplay());
    }
    #endregion

    #region Game Mode Selection
    public void SelectCargoMode()
    {
        loadingPanel.SetActive(true);
        StartCoroutine(LoadCargoMode());
    }

    public void SelectMode2()
    {
        loadingPanel.SetActive(true);
        StartCoroutine(LoadMode2());
    }
    #endregion
    #region Control Settings
    public void SetSteeringWheelControl()
    {
        RCC.SetMobileController(RCC_Settings.MobileController.SteeringWheel);
    }

    public void SetTouchScreenControl()
    {
        RCC.SetMobileController(RCC_Settings.MobileController.TouchScreen);
    }

    public void SetGyroControl()
    {
        RCC.SetMobileController(RCC_Settings.MobileController.Gyro);
    }
    #endregion

    #region External Links
    public void OpenRateUsPage()
    {
        Application.OpenURL("https://play.google.com/store/apps/details?id=com.smg.tractor.trolly.games.farming.game");
    }

    public void OpenPrivacyPolicy()
    {
        Application.OpenURL("https://simulatorgames2022.blogspot.com/2023/04/privacy-policy.html");
    }

    public void OpenMoreGames()
    {
        Application.OpenURL("https://play.google.com/store/apps/dev?id=6151632225219809775");
    }

    public void OpenMudJeepGame()
    {
        Application.OpenURL("https://play.google.com/store/apps/details?id=com.smg.offroadjeep.mudjeep.offtheroad.jeepgame.simulator.offroad.driving.games");
    }

    public void OpenTrainGame()
    {
        Application.OpenURL("https://play.google.com/store/apps/details?id=com.smg.city.train.simulator.zt.game&hl=en");
    }
    #endregion
    #region Loading Coroutines
    IEnumerator LoadGameplay()
    {
        yield return new WaitForSeconds(1f);
        yield return new WaitForSeconds(3f);
        SceneManager.LoadScene("gameplay");
    }

    IEnumerator LoadFarmingMode()
    {
        yield return new WaitForSeconds(4f);
        SceneManager.LoadScene("Farming mod");
    }

    IEnumerator LoadTractorMode()
    {
        yield return new WaitForSeconds(1f);
        yield return new WaitForSeconds(3f);
        SceneManager.LoadScene("tractortochan");
    }

    IEnumerator LoadCargoMode()
    {
        yield return new WaitForSeconds(3f);
        levelPanel.SetActive(true);
        loadingPanel.SetActive(false);
    }

    IEnumerator LoadMode2()
    {
        yield return new WaitForSeconds(3f);
        levelMode2Panel.SetActive(true);
        loadingPanel.SetActive(false);
    }

    IEnumerator LoadModeSelection()
    {
        yield return new WaitForSeconds(1f);
        yield return new WaitForSeconds(2f);
        modePanel.SetActive(true);
        loadingPanel.SetActive(false);
    }
    #endregion
}
